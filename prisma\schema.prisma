// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Shop {
  id        String   @id @default(cuid())
  shopDomain String  @unique
  accessToken String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Mainfreight API settings
  mainfreightApiKey String?
  mainfreightRegion String? @default("NZ")
  mainfreightAccountCode String?
  
  // Shipping settings
  defaultServiceLevel String? @default("LCL")
  enableShipping Boolean @default(false)
  
  // App settings
  isActive Boolean @default(true)
  
  // Alternative Shipping API Settings
  activeShippingProvider String @default("MAIN Словарь") // "MAIN Словарь" or "FURNITURE_WORLD"
  furnitureWorldApiUrl String? @default("https://mainfrieghtshipping-production.up.railway.app/")
  
  shippingRates ShippingRate[]
  
  @@map("shops")
}

model ShippingRate {
  id        String   @id @default(cuid())
  shopId    String
  shop      Shop     @relation(fields: [shopId], references: [id])
  
  // Rate calculation details
  origin    Json
  destination Json
  freightDetails Json
  
  // Mainfreight response
  charges   Json
  totalAmount Decimal
  
  // Shopify order details
  orderId   String?
  customerId String?
  
  createdAt DateTime @default(now())
  
  @@map("shipping_rates")
}

model AppSettings {
  id        String   @id @default(cuid())
  shopId    String   @unique
  
  // General settings
  settings  Json
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@map("app_settings")
} 