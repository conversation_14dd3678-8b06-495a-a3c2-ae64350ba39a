const express = require('express');
const { PrismaClient } = require('@prisma/client');
const axios = require('axios');

const router = express.Router();
const prisma = new PrismaClient();

// Middleware to verify shop authentication
async function verifyShop(req, res, next) {
  const { shop } = req.query;
  
  if (!shop) {
    return res.status(400).json({ error: 'Shop parameter is required' });
  }
  
  try {
    const shopRecord = await prisma.shop.findUnique({
      where: { shopDomain: shop }
    });
    
    if (!shopRecord) {
      return res.status(401).json({ error: 'Shop not found' });
    }
    
    req.shop = shopRecord;
    next();
  } catch (error) {
    console.error('Shop verification error:', error);
    res.status(500).json({ error: 'Verification failed' });
  }
}

// Helper function to verify shop access token and scopes
async function verifyShopAccess(shop) {
  try {
    const response = await axios.get(
      `https://${shop.shopDomain}/admin/api/2023-10/shop.json`,
      {
        headers: {
          'X-Shopify-Access-Token': shop.accessToken,
          'Content-Type': 'application/json'
        }
      }
    );
    
    console.log('Shop access verification successful:', response.data.shop?.name);
    return true;
  } catch (error) {
    console.error('Shop access verification failed:', {
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data
    });
    return false;
  }
}

// Helper function to register/update carrier service with Shopify
async function manageCarrierService(shop, enable = true) {
  if (!shop.accessToken) {
    throw new Error('Shop access token not available');
  }
  
  // First verify shop access
  const hasAccess = await verifyShopAccess(shop);
  if (!hasAccess) {
    throw new Error('Cannot access shop with current token - please reinstall the app');
  }

  const carrierServiceData = {
    carrier_service: {
      name: "Mainfreight Shipping",
      callback_url: `${process.env.HOST}/api/shipping/rates`,
      service_discovery: true,
      carrier_service_type: "api",
      format: "json"
    }
  };

  try {
    // First, try to get existing carrier services to see if one already exists
    const existingResponse = await axios.get(
      `https://${shop.shopDomain}/admin/api/2023-10/carrier_services.json`,
      {
        headers: {
          'X-Shopify-Access-Token': shop.accessToken,
          'Content-Type': 'application/json'
        }
      }
    );

    const existingServices = existingResponse.data.carrier_services || [];
    const mainfreightService = existingServices.find(service => 
      service.name === "Mainfreight Shipping" || 
      service.callback_url?.includes('/api/shipping/rates')
    );

    if (enable) {
      if (mainfreightService) {
        // Update existing carrier service
        const updateResponse = await axios.put(
          `https://${shop.shopDomain}/admin/api/2023-10/carrier_services/${mainfreightService.id}.json`,
          {
            carrier_service: {
              id: mainfreightService.id,
              active: true,
              ...carrierServiceData.carrier_service
            }
          },
          {
            headers: {
              'X-Shopify-Access-Token': shop.accessToken,
              'Content-Type': 'application/json'
            }
          }
        );
        return updateResponse.data.carrier_service;
      } else {
        // Create new carrier service
        const createResponse = await axios.post(
          `https://${shop.shopDomain}/admin/api/2023-10/carrier_services.json`,
          carrierServiceData,
          {
            headers: {
              'X-Shopify-Access-Token': shop.accessToken,
              'Content-Type': 'application/json'
            }
          }
        );
        return createResponse.data.carrier_service;
      }
    } else {
      // Disable the carrier service if it exists
      if (mainfreightService) {
        await axios.put(
          `https://${shop.shopDomain}/admin/api/2023-10/carrier_services/${mainfreightService.id}.json`,
          {
            carrier_service: {
              id: mainfreightService.id,
              active: false
            }
          },
          {
            headers: {
              'X-Shopify-Access-Token': shop.accessToken,
              'Content-Type': 'application/json'
            }
          }
        );
      }
      return null;
    }
  } catch (error) {
    console.error('Carrier service management error:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      headers: error.response?.headers
    });
    
    if (error.response?.status === 401) {
      throw new Error('Unauthorized: Invalid or expired access token. Please reinstall the app.');
    } else if (error.response?.status === 403) {
      throw new Error('Forbidden: App does not have permission to manage carrier services. Check app scopes.');
    } else if (error.response?.status === 422) {
      throw new Error(`Validation error: ${JSON.stringify(error.response?.data)}`);
    } else {
      throw new Error(`Carrier service error: ${error.message}`);
    }
  }
}

// Get shop settings
router.get('/', verifyShop, async (req, res) => {
  try {
    const settings = {
      mainfreightApiKey: req.shop.mainfreightApiKey || '',
      mainfreightRegion: req.shop.mainfreightRegion || 'NZ',
      mainfreightAccountCode: req.shop.mainfreightAccountCode || '',
      defaultServiceLevel: req.shop.defaultServiceLevel || 'LCL',
      enableShipping: req.shop.enableShipping || false,
      isActive: req.shop.isActive || false,
      activeShippingProvider: req.shop.activeShippingProvider || 'MAIN Словарь',
      furnitureWorldApiUrl: req.shop.furnitureWorldApiUrl || 'https://mainfrieghtshipping-production.up.railway.app/'
    };
    
    res.json(settings);
  } catch (error) {
    console.error('Get settings error:', error);
    res.status(500).json({ error: 'Failed to fetch settings' });
  }
});

// Update shop settings
router.put('/', verifyShop, async (req, res) => {
  const {
    mainfreightApiKey,
    mainfreightRegion,
    mainfreightAccountCode,
    defaultServiceLevel,
    enableShipping,
    activeShippingProvider,
    furnitureWorldApiUrl
  } = req.body;
  
  try {
    const updatedShop = await prisma.shop.update({
      where: { id: req.shop.id },
      data: {
        mainfreightApiKey,
        mainfreightRegion,
        mainfreightAccountCode,
        defaultServiceLevel,
        enableShipping,
        activeShippingProvider,
        furnitureWorldApiUrl,
        updatedAt: new Date()
      }
    });
    
    // Register or update carrier service with Shopify
    let carrierServiceResult = null;
    let carrierServiceError = null;
    
    if (enableShipping && mainfreightApiKey && mainfreightAccountCode) {
      try {
        carrierServiceResult = await manageCarrierService(updatedShop, true);
        console.log('Carrier service registered/updated:', carrierServiceResult?.id);
      } catch (error) {
        console.error('Failed to register carrier service:', error.message);
        carrierServiceError = error.message;
        // Don't fail the settings update if carrier service registration fails
      }
    } else if (!enableShipping) {
      try {
        await manageCarrierService(updatedShop, false);
        console.log('Carrier service disabled');
      } catch (error) {
        console.error('Failed to disable carrier service:', error.message);
        carrierServiceError = error.message;
      }
    }
    
    res.json({
      message: 'Settings updated successfully',
      carrierServiceActive: !!carrierServiceResult,
      carrierServiceError: carrierServiceError,
      settings: {
        mainfreightApiKey: updatedShop.mainfreightApiKey,
        mainfreightRegion: updatedShop.mainfreightRegion,
        mainfreightAccountCode: updatedShop.mainfreightAccountCode,
        defaultServiceLevel: updatedShop.defaultServiceLevel,
        enableShipping: updatedShop.enableShipping,
        activeShippingProvider: updatedShop.activeShippingProvider,
        furnitureWorldApiUrl: updatedShop.furnitureWorldApiUrl,
        isActive: updatedShop.isActive
      }
    });
  } catch (error) {
    console.error('Update settings error:', error);
    res.status(500).json({ error: 'Failed to update settings' });
  }
});

// Test Mainfreight API connection
router.post('/test-connection', verifyShop, async (req, res) => {
  const { apiKey, region, accountCode } = req.body;
  
  if (!apiKey || !region || !accountCode) {
    return res.status(400).json({ error: 'API key, region, and account code are required' });
  }
  
  try {
    // Test request to Mainfreight API
    const testPayload = {
      account: {
        code: accountCode
      },
      serviceLevel: {
        code: "LCL"
      },
      origin: {
        freightRequiredDateTime: new Date().toISOString(),
        address: {
          suburb: "Grey Lynn",
          postCode: "1011",
          city: "Auckland",
          countryCode: region
        }
      },
      destination: {
        address: {
          suburb: "Mount Cook",
          postCode: "6011",
          city: "Wellington",
          countryCode: region
        }
      },
      freightDetails: [{
        units: 1,
        packTypeCode: "PLT",
        height: 1.0,
        length: 1.0,
        width: 1.0,
        weight: 100
      }]
    };
    
    const response = await axios.post(
      `https://api.mainfreight.com/transport/1.0/customer/rate?region=${region}`,
      testPayload,
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Secret ${apiKey}`,
          'Accept': 'application/json'
        }
      }
    );
    
    res.json({
      success: true,
      message: 'Connection test successful',
      testResponse: response.data
    });
    
  } catch (error) {
    console.error('Connection test error:', error);
    
    if (error.response) {
      res.status(400).json({
        success: false,
        message: 'Mainfreight API connection failed',
        error: error.response.data || error.message
      });
    } else {
      res.status(500).json({
        success: false,
        message: 'Connection test failed',
        error: error.message
      });
    }
  }
});

// Get available regions
router.get('/regions', (req, res) => {
  const regions = [
    { code: 'NZ', name: 'New Zealand' },
    { code: 'AU', name: 'Australia' },
    { code: 'US', name: 'United States' },
    { code: 'EU', name: 'Europe' },
    { code: 'ASIA', name: 'Asia' }
  ];
  
  res.json(regions);
});

// Get available service levels
router.get('/service-levels', (req, res) => {
  const serviceLevels = [
    { code: 'LCL', name: 'Less than Container Load' },
    { code: 'FCL', name: 'Full Container Load' },
    { code: 'AIR', name: 'Air Freight' },
    { code: 'EXPRESS', name: 'Express' }
  ];
  
  res.json(serviceLevels);
});

// Manually register carrier service endpoint
router.post('/register-carrier-service', verifyShop, async (req, res) => {
  try {
    if (!req.shop.enableShipping || !req.shop.mainfreightApiKey || !req.shop.mainfreightAccountCode) {
      return res.status(400).json({ 
        error: 'Shipping must be enabled and API credentials configured before registering carrier service' 
      });
    }

    const carrierService = await manageCarrierService(req.shop, true);
    
    res.json({
      success: true,
      message: 'Carrier service registered successfully',
      carrierService: carrierService
    });
  } catch (error) {
    console.error('Manual carrier service registration error:', error);
    res.status(500).json({ 
      success: false,
      error: error.message || 'Failed to register carrier service'
    });
  }
});

module.exports = router; 