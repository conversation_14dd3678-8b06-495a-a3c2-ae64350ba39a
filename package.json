{"name": "mainfreight-shopify-app", "version": "1.0.0", "description": "Shopify app for Mainfreight shipping calculations", "main": "server.js", "scripts": {"start": "node server.js", "dev": "node server.js", "build": "prisma generate && prisma db push --accept-data-loss", "postinstall": "prisma generate", "db:generate": "prisma generate", "db:push": "prisma db push", "db:studio": "prisma studio", "db:migrate": "prisma migrate dev"}, "dependencies": {"@prisma/client": "^6.8.2", "axios": "^1.6.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "prisma": "^6.8.2"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "author": "Your Name", "license": "ISC"}