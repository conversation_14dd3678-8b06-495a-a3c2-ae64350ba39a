<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mainfreight Shipping - Shopify App</title>
    <link rel="stylesheet" href="https://unpkg.com/@shopify/polaris@11.0.0/build/esm/styles.css">
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background-color: #f6f6f7;
        }
        
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background-color: #f6f6f7;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #e1e1e1;
            border-top: 4px solid #008060;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background-color: #f6f6f7;
            color: #d72c0d;
            text-align: center;
            padding: 20px;
        }
        
        .error h1 {
            margin-bottom: 10px;
        }
        
        .error p {
            margin-bottom: 20px;
            max-width: 400px;
        }
        
        .retry-btn {
            background-color: #008060;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .retry-btn:hover {
            background-color: #006b4f;
        }
    </style>
</head>
<body>
    <div id="root">
        <div class="loading">
            <div class="loading-spinner"></div>
        </div>
    </div>

    <!-- React -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>
    
    <!-- Axios for HTTP requests -->
    <script src="https://unpkg.com/axios@1.6.0/dist/axios.min.js"></script>
    
    <!-- Main App Scripts -->
    <script src="/app.js"></script>
    <script src="/app-components.js"></script>
</body>
</html> 