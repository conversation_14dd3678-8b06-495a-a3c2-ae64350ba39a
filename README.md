# Mainfreight Shopify Shipping App

A Shopify app that integrates with the Mainfreight API to provide real-time shipping rate calculations during checkout.

## Features

- 🚢 **Real-time Shipping Calculations**: Get accurate shipping rates from Mainfreight API during checkout
- ⚙️ **Easy Configuration**: Simple settings page to configure API keys, regions, and account details
- 📊 **Shipping History**: Track all shipping rate calculations and their costs
- 🔧 **Test Connection**: Validate your Mainfreight API credentials before going live
- 🌍 **Multi-Region Support**: Supports NZ, Australia, US, Europe, and Asia regions
- 📱 **Modern UI**: Clean, responsive interface built with Shopify Polaris design system

## Prerequisites

- Node.js 16+ and npm
- PostgreSQL database
- Shopify Partner account
- Mainfreight API credentials

## Quick Start

### 1. <PERSON><PERSON> and Install

```bash
git clone <repository-url>
cd mainfreight-shopify-app
npm install
```

### 2. Database Setup

```bash
# Set up your PostgreSQL database
# Copy environment variables
cp env.example .env

# Edit .env with your configuration
# DATABASE_URL="postgresql://username:password@localhost:5432/mainfreight_shopify"

# Generate Prisma client and run migrations
npm run db:generate
npm run db:push
```

### 3. Shopify App Configuration

1. Create a new app in your Shopify Partner dashboard
2. Configure OAuth redirect URLs:
   - `https://your-app-url.com/api/auth/shopify/callback`
3. Set required scopes: `write_shipping_rates,read_orders,write_orders,read_customers,write_customers`
4. Update your `.env` file with Shopify app credentials

### 4. Environment Variables

Create a `.env` file with the following variables:

```env
# Shopify App Configuration
SHOPIFY_API_KEY=your_shopify_api_key
SHOPIFY_API_SECRET=your_shopify_api_secret
SCOPES=write_shipping_rates,read_orders,write_orders,read_customers,write_customers
HOST=https://your-app-url.com

# Database
DATABASE_URL="postgresql://username:password@localhost:5432/mainfreight_shopify"

# Mainfreight API Configuration
MAINFREIGHT_API_KEY=your_mainfreight_api_key
MAINFREIGHT_BASE_URL=https://api.mainfreight.com/transport/1.0/customer

# App Configuration
PORT=8080
NODE_ENV=development
```

### 5. Run the Application

```bash
# Development
npm run dev

# Production
npm run build
npm start
```

## Shopify Setup

### 1. Install the App

1. In your development store, go to Apps
2. Click "Install unlisted app"
3. Enter your app URL: `https://your-app-url.com?shop=your-store.myshopify.com`

### 2. Configure Carrier Service

The app automatically registers itself as a carrier service with Shopify when properly configured. The shipping rates will appear during checkout when:

- The app is installed and configured
- Mainfreight API credentials are valid
- Shipping is enabled in app settings

## Configuration

### App Settings

Access the app settings page to configure:

1. **Mainfreight API Key**: Your unique API key from Mainfreight
2. **Region**: Select your operating region (NZ, AU, US, EU, ASIA)
3. **Account Code**: Your Mainfreight account number
4. **Service Level**: Default shipping service (LCL, FCL, AIR, EXPRESS)
5. **Enable Shipping**: Toggle to activate/deactivate shipping calculations

### Testing Connection

Use the "Test Connection" button to verify your Mainfreight API credentials before enabling shipping calculations.

## How It Works

### Shipping Rate Calculation

1. Customer proceeds to checkout
2. Shopify calls the app's carrier service endpoint (`/api/shipping/rates`)
3. App maps Shopify address format to Mainfreight API format
4. App calculates freight details from cart items (weight, volume, units)
5. App calls Mainfreight API with shipping request
6. App returns formatted shipping rates to Shopify
7. Customer sees Mainfreight shipping options at checkout

### Address Mapping

The app automatically maps Shopify addresses to Mainfreight format based on region:

- **New Zealand**: suburb, city, postCode, countryCode
- **Australia**: town, postCode, stateCode, countryCode  
- **Other regions**: city, postCode, stateCode, countryCode

### Freight Calculation

The app estimates freight details from cart items:
- **Weight**: Converts grams to kilograms
- **Volume**: Estimates based on weight (1kg ≈ 0.01m³)
- **Units**: Sum of all item quantities
- **Package Type**: Defaults to "CTN" (carton)

## API Endpoints

### Public Endpoints

- `POST /api/shipping/rates` - Shopify carrier service endpoint
- `POST /api/webhooks/*` - Shopify webhook handlers

### Admin Endpoints (require shop authentication)

- `GET /api/settings` - Get app settings
- `PUT /api/settings` - Update app settings  
- `POST /api/settings/test-connection` - Test Mainfreight API
- `GET /api/shipping/history` - Get shipping calculation history
- `POST /api/shipping/calculate` - Manual rate calculation

## Database Schema

### Shop
- Stores Shopify shop information and Mainfreight configuration
- Fields: shopDomain, accessToken, mainfreightApiKey, mainfreightRegion, etc.

### ShippingRate  
- Logs all shipping rate calculations
- Fields: origin, destination, freightDetails, charges, totalAmount

### AppSettings
- Stores additional app configuration as JSON
- Extensible for future settings

## Development

### Project Structure

```
├── api/                    # API routes
│   ├── auth.js            # Shopify OAuth handling
│   ├── settings.js        # App settings management
│   ├── shipping.js        # Shipping rate calculations
│   └── webhooks.js        # Shopify webhook handlers
├── prisma/
│   └── schema.prisma      # Database schema
├── public/                # Frontend files
│   ├── index.html         # Main HTML
│   ├── app.js            # React components (part 1)
│   └── app-components.js  # React components (part 2)
├── server.js              # Express server
├── package.json
└── shopify.app.toml      # Shopify app configuration
```

### Adding New Features

1. **Backend**: Add new API routes in the `api/` directory
2. **Frontend**: Extend React components in `public/app.js`
3. **Database**: Update Prisma schema and run migrations

### Testing

```bash
# Test Mainfreight API connection
curl -X POST "http://localhost:8080/api/settings/test-connection?shop=your-store.myshopify.com" \
  -H "Content-Type: application/json" \
  -d '{
    "apiKey": "your_api_key",
    "region": "NZ", 
    "accountCode": "your_account_code"
  }'

# Test shipping rate calculation
curl -X POST "http://localhost:8080/api/shipping/calculate?shop=your-store.myshopify.com" \
  -H "Content-Type: application/json" \
  -d '{
    "origin": {
      "suburb": "Grey Lynn",
      "city": "Auckland", 
      "postCode": "1011",
      "countryCode": "NZ"
    },
    "destination": {
      "suburb": "Mount Cook",
      "city": "Wellington",
      "postCode": "6011", 
      "countryCode": "NZ"
    },
    "freightDetails": [{
      "units": 1,
      "packTypeCode": "PLT",
      "height": 1.23,
      "length": 1.45,
      "width": 1.6,
      "weight": 120
    }]
  }'
```

## Deployment

### Heroku

1. Create Heroku app: `heroku create your-app-name`
2. Add PostgreSQL addon: `heroku addons:create heroku-postgresql:hobby-dev`
3. Set environment variables: `heroku config:set SHOPIFY_API_KEY=xxx`
4. Deploy: `git push heroku main`

### Railway

1. Connect your GitHub repository
2. Add PostgreSQL database
3. Set environment variables in Railway dashboard
4. Deploy automatically on git push

### AWS/DigitalOcean

1. Set up Node.js server with PM2
2. Configure PostgreSQL database
3. Set up reverse proxy with Nginx
4. Configure SSL certificates

## Troubleshooting

### Common Issues

1. **"Shop not found" error**: Ensure shop domain is passed in URL query parameter
2. **Mainfreight API errors**: Check API key, region, and account code are correct
3. **No shipping rates appearing**: Verify shipping is enabled and API connection works
4. **Database connection errors**: Check DATABASE_URL format and database accessibility

### Debugging

Enable debug logging by setting `NODE_ENV=development` and check:
- Server logs for API errors
- Network tab in browser for failed requests
- Database connectivity with `npm run db:studio`

## Support

For issues with:
- **Mainfreight API**: Contact Mainfreight developer support
- **Shopify Integration**: Check Shopify Partner documentation
- **App Issues**: Create an issue in this repository

## License

MIT License - see LICENSE file for details.

---

Built with ❤️ for Shopify merchants using Mainfreight shipping services. 