// Shopify App with React (using UMD builds for simplicity)
const { useState, useEffect, useCallback } = React;

// Get shop domain from URL parameters
function getShopDomain() {
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.get('shop');
}

// API helper functions
const api = {
  get: (endpoint) => axios.get(`/api${endpoint}?shop=${getShopDomain()}`),
  post: (endpoint, data) => axios.post(`/api${endpoint}?shop=${getShopDomain()}`, data),
  put: (endpoint, data) => axios.put(`/api${endpoint}?shop=${getShopDomain()}`, data),
};

// Toast notification component
function Toast({ message, type, onDismiss }) {
  useEffect(() => {
    const timer = setTimeout(onDismiss, 5000);
    return () => clearTimeout(timer);
  }, [onDismiss]);

  const bgColor = type === 'success' ? '#008060' : type === 'error' ? '#d72c0d' : '#0084ff';

  return React.createElement('div', {
    style: {
      position: 'fixed',
      top: '20px',
      right: '20px',
      backgroundColor: bgColor,
      color: 'white',
      padding: '12px 20px',
      borderRadius: '4px',
      boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
      zIndex: 1000,
      maxWidth: '300px'
    }
  }, message);
}

// Loading component
function Loading() {
  return React.createElement('div', {
    className: 'loading'
  }, React.createElement('div', {
    className: 'loading-spinner'
  }));
}

// Settings Form Component
function SettingsForm({ settings, onSave }) {
  const [formData, setFormData] = useState(settings);
  const [loading, setLoading] = useState(false);
  const [testing, setTesting] = useState(false);

  useEffect(() => {
    setFormData(settings);
  }, [settings]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    try {
      await onSave(formData);
    } finally {
      setLoading(false);
    }
  };

  const testConnection = async () => {
    if (formData.activeShippingProvider === 'MAIN Словарь') {
      if (!formData.mainfreightApiKey || !formData.mainfreightRegion || !formData.mainfreightAccountCode) {
        alert('Please fill in Mainfreight API key, region, and account code before testing');
        return;
      }
      setTesting(true);
      try {
        const response = await api.post('/settings/test-connection', {
          apiKey: formData.mainfreightApiKey,
          region: formData.mainfreightRegion,
          accountCode: formData.mainfreightAccountCode
        });
        if (response.data.success) {
          alert('Mainfreight connection test successful!');
        } else {
          alert('Mainfreight connection test failed: ' + response.data.message);
        }
      } catch (error) {
        alert('Mainfreight connection test failed: ' + (error.response?.data?.message || error.message));
      } finally {
        setTesting(false);
      }
    } else if (formData.activeShippingProvider === 'FURNITURE_WORLD') {
      if (!formData.furnitureWorldApiUrl) {
        alert('Please fill in the Furniture World API URL before testing');
        return;
      }
      setTesting(true);
      try {
        // Test Furniture World API (e.g., by making a sample /calculate_shipping call)
        const testPayload = {
          place_name: "TAIRUA", // Example data
          suburb_name: "",
          actual_m3: 1.0,
          weight_kg: 10.0
        };
        const response = await axios.post(`${formData.furnitureWorldApiUrl.replace(/\/$/, '')}/calculate_shipping`, testPayload);
        if (response.data && response.data.cost) {
            alert('Furniture World API connection test successful! Sample cost: ' + response.data.cost);
        } else {
            alert('Furniture World API connection test likely failed. Response: ' + JSON.stringify(response.data));
        }
      } catch (error) {
        alert('Furniture World API connection test failed: ' + (error.response?.data?.error || error.message));
      } finally {
        setTesting(false);
      }
    }
  };

  return React.createElement('div', {
    style: { maxWidth: '600px', margin: '0 auto', padding: '20px' }
  }, [
    React.createElement('h2', {
      key: 'title',
      style: { marginBottom: '24px', color: '#212b36' }
    }, 'Mainfreight Shipping Settings'),
    
    React.createElement('form', {
      key: 'form',
      onSubmit: handleSubmit,
      style: { backgroundColor: 'white', padding: '24px', borderRadius: '8px', boxShadow: '0 1px 3px rgba(0,0,0,0.1)' }
    }, [
      // Active Shipping Provider field
      React.createElement('div', {
        key: 'provider',
        style: { marginBottom: '20px' }
      }, [
        React.createElement('label', {
          key: 'label',
          style: { display: 'block', marginBottom: '8px', fontWeight: '500', color: '#212b36' }
        }, 'Active Shipping Provider'),
        React.createElement('select', {
          key: 'select_provider',
          value: formData.activeShippingProvider || 'MAIN Словарь',
          onChange: (e) => handleInputChange('activeShippingProvider', e.target.value),
          style: {
            width: '100%',
            padding: '12px',
            border: '1px solid #d9d9d9',
            borderRadius: '4px',
            fontSize: '14px'
          }
        }, [
          React.createElement('option', { key: 'mainfreight', value: 'MAIN Словарь' }, 'Mainfreight API'),
          React.createElement('option', { key: 'furniture_world', value: 'FURNITURE_WORLD' }, 'Furniture World API')
        ])
      ]),

      // Conditional fields based on provider
      formData.activeShippingProvider === 'MAIN Словарь' && React.createElement(React.Fragment, { key: 'mainfreight_fields' }, [
        // API Key field
        React.createElement('div', {
          key: 'apikey',
          style: { marginBottom: '20px' }
        }, [
          React.createElement('label', {
            key: 'label_apikey',
            style: { display: 'block', marginBottom: '8px', fontWeight: '500', color: '#212b36' }
          }, 'Mainfreight API Key *'),
          React.createElement('input', {
            key: 'input_apikey',
            type: 'password',
            value: formData.mainfreightApiKey || '',
            onChange: (e) => handleInputChange('mainfreightApiKey', e.target.value),
            placeholder: 'Enter your Mainfreight API key',
            style: {
              width: '100%',
              padding: '12px',
              border: '1px solid #d9d9d9',
              borderRadius: '4px',
              fontSize: '14px'
            }
          })
        ]),

        // Region field
        React.createElement('div', {
          key: 'region',
          style: { marginBottom: '20px' }
        }, [
          React.createElement('label', {
            key: 'label_region',
            style: { display: 'block', marginBottom: '8px', fontWeight: '500', color: '#212b36' }
          }, 'Region *'),
          React.createElement('select', {
            key: 'select_region',
            value: formData.mainfreightRegion || 'NZ',
            onChange: (e) => handleInputChange('mainfreightRegion', e.target.value),
            style: {
              width: '100%',
              padding: '12px',
              border: '1px solid #d9d9d9',
              borderRadius: '4px',
              fontSize: '14px'
            }
          }, [
            React.createElement('option', { key: 'nz', value: 'NZ' }, 'New Zealand'),
            React.createElement('option', { key: 'au', value: 'AU' }, 'Australia'),
            React.createElement('option', { key: 'us', value: 'US' }, 'United States'),
            React.createElement('option', { key: 'eu', value: 'EU' }, 'Europe'),
            React.createElement('option', { key: 'asia', value: 'ASIA' }, 'Asia')
          ])
        ]),

        // Account Code field
        React.createElement('div', {
          key: 'account',
          style: { marginBottom: '20px' }
        }, [
          React.createElement('label', {
            key: 'label_account',
            style: { display: 'block', marginBottom: '8px', fontWeight: '500', color: '#212b36' }
          }, 'Account Code *'),
          React.createElement('input', {
            key: 'input_account',
            type: 'text',
            value: formData.mainfreightAccountCode || '',
            onChange: (e) => handleInputChange('mainfreightAccountCode', e.target.value),
            placeholder: 'Enter your Mainfreight account code',
            style: {
              width: '100%',
              padding: '12px',
              border: '1px solid #d9d9d9',
              borderRadius: '4px',
              fontSize: '14px'
            }
          })
        ]),

        // Service Level field  
        React.createElement('div', {
          key: 'service',
          style: { marginBottom: '20px' }
        }, [
          React.createElement('label', {
            key: 'label_service',
            style: { display: 'block', marginBottom: '8px', fontWeight: '500', color: '#212b36' }
          }, 'Default Service Level'),
          React.createElement('select', {
            key: 'select_service',
            value: formData.defaultServiceLevel || 'LCL',
            onChange: (e) => handleInputChange('defaultServiceLevel', e.target.value),
            style: {
              width: '100%',
              padding: '12px',
              border: '1px solid #d9d9d9',
              borderRadius: '4px',
              fontSize: '14px'
            }
          }, [
            React.createElement('option', { key: 'lcl', value: 'LCL' }, 'Less than Container Load'),
            React.createElement('option', { key: 'fcl', value: 'FCL' }, 'Full Container Load'),
            React.createElement('option', { key: 'air', value: 'AIR' }, 'Air Freight'),
            React.createElement('option', { key: 'express', value: 'EXPRESS' }, 'Express')
          ])
        ])
      ]),

      formData.activeShippingProvider === 'FURNITURE_WORLD' && React.createElement(React.Fragment, { key: 'furniture_world_fields' }, [
        // Furniture World API URL field
        React.createElement('div', {
          key: 'fw_api_url',
          style: { marginBottom: '20px' }
        }, [
          React.createElement('label', {
            key: 'label_fw_api_url',
            style: { display: 'block', marginBottom: '8px', fontWeight: '500', color: '#212b36' }
          }, 'Furniture World API URL *'),
          React.createElement('input', {
            key: 'input_fw_api_url',
            type: 'url',
            value: formData.furnitureWorldApiUrl || 'https://mainfrieghtshipping-production.up.railway.app/',
            onChange: (e) => handleInputChange('furnitureWorldApiUrl', e.target.value),
            placeholder: 'Enter Furniture World API Base URL',
            style: {
              width: '100%',
              padding: '12px',
              border: '1px solid #d9d9d9',
              borderRadius: '4px',
              fontSize: '14px'
            }
          })
        ])
      ]),

      // Enable Shipping checkbox
      React.createElement('div', {
        key: 'enable',
        style: { marginBottom: '24px' }
      }, [
        React.createElement('label', {
          key: 'label_enable',
          style: { display: 'flex', alignItems: 'center', cursor: 'pointer' }
        }, [
          React.createElement('input', {
            key: 'checkbox_enable',
            type: 'checkbox',
            checked: formData.enableShipping || false,
            onChange: (e) => handleInputChange('enableShipping', e.target.checked),
            style: { marginRight: '8px' }
          }),
          React.createElement('span', { key: 'text_enable' }, 'Enable shipping calculations')
        ])
      ]),

      // Buttons
      React.createElement('div', {
        key: 'buttons',
        style: { display: 'flex', gap: '12px', justifyContent: 'flex-end' }
      }, [
        React.createElement('button', {
          key: 'test',
          type: 'button',
          onClick: testConnection,
          disabled: testing,
          style: {
            padding: '12px 24px',
            backgroundColor: '#0084ff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: testing ? 'not-allowed' : 'pointer',
            fontSize: '14px',
            opacity: testing ? 0.6 : 1
          }
        }, testing ? 'Testing...' : 'Test Connection'),
        
        React.createElement('button', {
          key: 'save',
          type: 'submit',
          disabled: loading,
          style: {
            padding: '12px 24px',
            backgroundColor: '#008060',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: loading ? 'not-allowed' : 'pointer',
            fontSize: '14px',
            opacity: loading ? 0.6 : 1
          }
        }, loading ? 'Saving...' : 'Save Settings')
      ])
    ])
  ]);
} 