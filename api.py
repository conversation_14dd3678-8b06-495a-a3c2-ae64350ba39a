from flask import Flask, request, jsonify
import csv
import os

# --- Core Data Processing and Calculation Logic (Copied from GUI version) ---
# Make sure this is the same up-to-date version from your working GUI
def parse_currency(value_str):
    if isinstance(value_str, (int, float)):
        return float(value_str)
    cleaned_str = value_str.strip() if isinstance(value_str, str) else ""
    if not cleaned_str:
        return None
    try:
        cleaned_str = cleaned_str.replace('\xa0', '') 
        return float(cleaned_str.replace('$', '').replace(',', ''))
    except ValueError:
        return None

# Global variable to store loaded shipping data
SHIPPING_DATA_GLOBAL = None
# File paths - these should be configured or passed at startup in a real app
# For simplicity, we'll define them here. Make sure they point to your actual files.
# Ideally, these would come from environment variables or a config file.
DELIVERY_STRUCTURE_FILE_PATH = 'Furniture World - M2H Rate Proposal.xlsx - Delivery Structure.csv' # This file seems unused in current logic, but keeping path
AREAS_FILE_PATH = 'Furniture World - M2H Rate Proposal.xlsx - 2Home Areas.csv'
RATE_APPLICATION_FILE_PATH = 'Furniture World - M2H Rate Proposal.xlsx - Rate Application.csv'


def load_shipping_data_on_startup():
    global SHIPPING_DATA_GLOBAL
    if SHIPPING_DATA_GLOBAL is None: # Load only once
        print("Loading shipping data for API...")
        try:
            # Ensure file paths are correct and files exist
            if not os.path.exists(DELIVERY_STRUCTURE_FILE_PATH):
                raise FileNotFoundError(f"Delivery Structure file not found: {DELIVERY_STRUCTURE_FILE_PATH}")
            if not os.path.exists(AREAS_FILE_PATH):
                raise FileNotFoundError(f"2Home Areas file not found: {AREAS_FILE_PATH}")
            if not os.path.exists(RATE_APPLICATION_FILE_PATH):
                raise FileNotFoundError(f"Rate Application file not found: {RATE_APPLICATION_FILE_PATH}")

            SHIPPING_DATA_GLOBAL = process_csv_files(
                DELIVERY_STRUCTURE_FILE_PATH,
                AREAS_FILE_PATH,
                RATE_APPLICATION_FILE_PATH
            )
            print("Shipping data loaded successfully for API.")
        except Exception as e:
            print(f"CRITICAL ERROR: Failed to load shipping data on API startup: {e}")
            # In a production app, you might want to prevent the API from starting
            # or have a health check endpoint that reflects this failure.
            SHIPPING_DATA_GLOBAL = {"error": str(e)} # Store error for API responses
    return SHIPPING_DATA_GLOBAL


def process_csv_files(delivery_structure_file, areas_file, rate_application_file):
    print(f"\n--- Processing Files ---")
    
    locations_map = {} 
    customer_locations_data = [] 
    # New structures for rate calculation
    city_base_rates = {} # CITY_KEY_STR: base_rate_float
    area_data = {} # AREA_RATING_STR: {"delivery_fee": float, "extra_per_m3": float}
    m3_conversion_kg_to_m3_factor = 0.006 # As per CSV note "m3 conversion factor of 6:1" which implies 1kg = 0.006m3 (if 1m3=166.67kg) or 1/166.67, will confirm value needed. The file example shows 75kg -> 0.45m3, so 0.45/75 = 0.006

    # 1. Process 2Home Areas CSV 
    try:
        print(f"Processing: {os.path.basename(areas_file)}")
        with open(areas_file, 'r', encoding='utf-8-sig') as f:
            reader = csv.DictReader(f)
            for row_dict in reader:
                place = row_dict.get('Place Name','').strip().upper()
                suburb = row_dict.get('Suburb Name','').strip().upper()
                area_rating_str = row_dict.get('Delivery Rating Area','').strip() 
                rate_table_key_city = row_dict.get('Delivery Rating Place','').strip().upper()

                if place and area_rating_str and rate_table_key_city:
                    locations_map[(place, suburb if suburb else "")] = {
                        "area_rating": area_rating_str, # Will be "1", "2"..."13", or "QRD"
                        "rate_table_city_key": rate_table_key_city
                    }
                    customer_locations_data.append({
                        "place_name": place,
                        "suburb_name": suburb if suburb else ""
                    })
        print(f"  Found {len(locations_map)} place/suburb to area_rating/city_key mappings.")
    except Exception as e:
        raise Exception(f"Error processing 2Home Areas file '{os.path.basename(areas_file)}': {e}")

    # 2. Process Rate Application CSV
    try:
        print(f"Processing: {os.path.basename(rate_application_file)}")
        with open(rate_application_file, 'r', encoding='utf-8-sig') as f:
            all_rows = list(csv.reader(f))
            if len(all_rows) < 12: # Check if enough rows for headers and at least one data row
                raise ValueError("Rate Application CSV: Too few rows for parsing rate structure.")

            # --- Parse Area Delivery Fees and Extra $ per m3 ---
            # Area definitions are in Row 8 (index 7), fees in Row 9 (index 8), extra_per_m3 in Row 10 (index 9)
            area_rating_def_row = all_rows[7] # Row 8 in Excel (0-indexed)
            area_delivery_fee_row = all_rows[8] # Row 9
            area_extra_per_m3_row = all_rows[9] # Row 10

            if len(area_rating_def_row) <= 6 or len(area_delivery_fee_row) <= 6 or len(area_extra_per_m3_row) <=6:
                raise ValueError("Rate Application CSV: Not enough columns in Area definition/fee rows (Rows 8-10).")

            # Area ratings start at Column G (index 6)
            for c_idx in range(6, len(area_rating_def_row)):
                area_r_val = area_rating_def_row[c_idx].strip()
                if area_r_val.isdigit() or area_r_val.upper() == "QRD": # "1", "2", ..., "13" or "QRD"
                    delivery_fee_str = area_delivery_fee_row[c_idx] if c_idx < len(area_delivery_fee_row) else None
                    extra_per_m3_str = area_extra_per_m3_row[c_idx] if c_idx < len(area_extra_per_m3_row) else None
                    
                    delivery_fee = parse_currency(delivery_fee_str)
                    extra_per_m3 = parse_currency(extra_per_m3_str)

                    if delivery_fee is not None and extra_per_m3 is not None:
                        area_data[area_r_val] = {
                            "delivery_fee": delivery_fee,
                            "extra_per_m3": extra_per_m3
                        }
                    # else:
                        # print(f"  RateApp Area {area_r_val}: Fee '{delivery_fee_str}' or Extra '{extra_per_m3_str}' invalid.")
            
            print(f"  Parsed area_data for {len(area_data)} areas from Rate App CSV.")
            if not area_data:
                 raise ValueError("Rate Application CSV: Could not parse any Area data (delivery_fee, extra_per_m3).")


            # --- Parse City/Depot Base Rate per m3 ---
            # City/Depot Location is in Col C (index 2) or Col F for fallback (index 5)
            # Base Rate per m3 is in Col C (index 2) in the first block, but the key is actually Col F (index 5) for matching the rate table's City/Depot Key.
            # The actual "City_Base_Rate_per_m3" as per user's definition is in Column C (index 2) of rows 12 onwards.
            # The "City/Depot Location" key for this base rate is also in Column C (index 2).
            # However, the main rate table uses keys from Column F (index 5) or C (index 2 as fallback).
            # For City_Base_Rate_per_m3, user said: "found in column C (labeled "Per m3") next to each city/depot location in the first block of city data."
            # Rows 12 onwards. City Key Col C (index 2), Base Rate Col D (index 3).

            # Corrected parsing for City Base Rates:
            # City/Depot Location (key) from Col C (index 2)
            # City_Base_Rate_per_m3 from Col D (index 3) -- "Per m3" column
            # This applies to the first block of cities (e.g. rows 12-27 for North Island)
            # Then similar block for South Island.
            # The "City/Depot Location" in Col F seems to be a key for the *second* table structure which we are moving away from.

            current_row_index = 11 # Start processing from row 12 (0-indexed 11)
            while current_row_index < len(all_rows):
                rate_row = all_rows[current_row_index]
                if len(rate_row) > 3: # Need at least up to Col D for City Name and Base Rate
                    city_key_col_c = rate_row[2].strip().upper() # City Name from Col C
                    base_rate_str_col_d = rate_row[3].strip()   # Base Rate from Col D ("Per m3")
                    
                    if city_key_col_c and base_rate_str_col_d: # If both city name and base rate are present
                        base_rate = parse_currency(base_rate_str_col_d)
                        if base_rate is not None:
                            city_base_rates[city_key_col_c] = base_rate
                        # else:
                        #     print(f"  RateApp Row {current_row_index+1}, City {city_key_col_c}: Base rate '{base_rate_str_col_d}' invalid.")
                    elif not city_key_col_c and not base_rate_str_col_d and len(rate_row) > 5 and rate_row[5].strip() : # check for a city key in Col F to indicate new section or end
                        # This might be a separator or the start of the second table which we are mostly ignoring for base rates.
                        # If we hit a row that looks like a header for the second table part, we might stop or skip.
                        # For now, we just check if Col C is empty. If empty, probably not a base rate definition row.
                        pass # continue, or break if it's a clear separator
                    elif not city_key_col_c and not any(c.strip() for c in rate_row): # empty row
                        pass # skip empty rows
                    
                current_row_index += 1
                # Heuristic to stop processing base rates if we encounter rows that are clearly not part of the base rate list
                # e.g., if we encounter a line that looks like "City/Depot Location,Per m3,,City/Depot Location,1,2,3..." again
                if current_row_index < len(all_rows) and len(all_rows[current_row_index]) > 5 and "City/Depot Location" in all_rows[current_row_index][0] and "Per m3" in all_rows[current_row_index][1]:
                    # This looks like a header for another block, or the detailed rate table. We are interested in the first list of base rates.
                    # For simplicity, we assume the base rates are listed before the main detailed grid headers repeat.
                    # This logic might need refinement if the CSV structure is more complex.
                    # For the provided CSV, base rates are in C and D, then there's a blank row, then South Island base rates in C and D.
                    pass # Continue, as there could be multiple blocks of base rates.
            
            print(f"  Parsed city_base_rates for {len(city_base_rates)} cities from Rate App CSV.")
            if not city_base_rates:
                raise ValueError("Rate Application CSV: Could not parse any City Base Rates.")

    except Exception as e:
        raise Exception(f"Error processing Rate Application file '{os.path.basename(rate_application_file)}': {e}")

    if not locations_map:
        raise Exception("Failed to load: locations_map (from 2HomeAreas.csv) is empty.")
    if not city_base_rates:
         raise Exception("Failed to load: city_base_rates (from RateApplication.csv) is empty.")
    if not area_data:
         raise Exception("Failed to load: area_data (from RateApplication.csv) is empty.")

    return {
        "locations_map": locations_map, 
        "customer_locations_for_gui": customer_locations_data, # Still useful for GUI if it lists locations
        "city_base_rates": city_base_rates,   # New structure
        "area_data": area_data,             # New structure
        "m3_conversion_kg_to_m3_factor": m3_conversion_kg_to_m3_factor,
    }
# --- End of Core Logic ---


app = Flask(__name__)

with app.app_context():
    load_shipping_data_on_startup()


@app.route('/calculate_shipping', methods=['POST'])
def calculate_shipping_api():
    shipping_data = SHIPPING_DATA_GLOBAL # Use the globally loaded data
    
    # Check if data failed to load on startup
    if shipping_data is None or "error" in shipping_data:
        error_msg = shipping_data.get("error", "Shipping data not available or failed to load on startup.") if shipping_data else "Shipping data not available."
        return jsonify({"error": error_msg, "cost": None, "breakdown": []}), 500

    data = request.get_json()
    if not data:
        return jsonify({"error": "Invalid JSON payload"}), 400

    place_name_input = data.get('place_name', '').strip().upper()
    suburb_name_input = data.get('suburb_name', '').strip().upper()
    
    # Handle missing place_name more gracefully
    if not place_name_input:
        # Check if we have a combined address in suburb_name that we can parse
        if suburb_name_input and ',' in suburb_name_input:
            parts = suburb_name_input.split(',', 1)
            place_name_input = parts[0].strip().upper()
            suburb_name_input = parts[1].strip().upper()
        elif suburb_name_input and '-' in suburb_name_input:
            parts = suburb_name_input.split('-', 1)
            place_name_input = parts[0].strip().upper()
            suburb_name_input = parts[1].strip().upper()
        else:
            return jsonify({"error": "place_name is required"}), 400
    
    try:
        actual_m3 = float(data.get('actual_m3', 0))
        weight_kg = float(data.get('weight_kg', 0))
    except (TypeError, ValueError):
        return jsonify({"error": "actual_m3 and weight_kg must be valid numbers"}), 400

    breakdown = [] # To store calculation steps

    # 1. Determine chargeable m³ (Order_m3)
    weight_equivalent_m3 = weight_kg * shipping_data["m3_conversion_kg_to_m3_factor"]
    # Ensure Order_m3 is at least 0.01 as per previous logic, or a minimum if specified by business rule
    calculated_chargeable_m3 = max(actual_m3, weight_equivalent_m3, 0.01) 
    order_m3 = calculated_chargeable_m3 # This is the Order_m3 for the formula

    breakdown.append(f"Actual Volume: {actual_m3:.2f} m³")
    breakdown.append(f"Weight Equiv. Volume: {weight_equivalent_m3:.3f} m³ ({weight_kg:.1f} kg)")
    breakdown.append(f"Calculated Chargeable Volume (Order_m3): {order_m3:.3f} m³")

    # 2. Get location specific data (Area Rating and Rate Table City Key)
    location_key_tuple = (place_name_input, suburb_name_input if suburb_name_input else "")
    location_details = shipping_data["locations_map"].get(location_key_tuple)
    
    # Try different combinations if not found
    if not location_details:
        # Try without suburb
        location_key_tuple_no_suburb = (place_name_input, "")
        location_details = shipping_data["locations_map"].get(location_key_tuple_no_suburb)
        
        if location_details:
            breakdown.append(f"Note: Using base rate for '{place_name_input}' without suburb.")
        else:
            # Try to extract city name from combined input
            possible_cities = []
            
            # Check if place_name contains a comma (CITY, SUBURB format)
            if ',' in place_name_input:
                parts = place_name_input.split(',', 1)
                possible_cities.append((parts[0].strip(), ""))
                possible_cities.append((parts[0].strip(), parts[1].strip()))
                
            # Check if place_name contains a dash (CITY - ADDRESS format)
            if '-' in place_name_input:
                parts = place_name_input.split('-', 1)
                possible_cities.append((parts[0].strip(), ""))
            
            # Try each possible city
            for city_key in possible_cities:
                location_details = shipping_data["locations_map"].get(city_key)
                if location_details:
                    breakdown.append(f"Note: Found match for '{city_key[0]}{' - ' + city_key[1] if city_key[1] else ''}'")
                    break
    
    # If still not found, try to find a partial match
    if not location_details:
        # Try to find a partial match for the city name
        for loc_key, loc_value in shipping_data["locations_map"].items():
            city_name = loc_key[0]
            if city_name in place_name_input or place_name_input in city_name:
                location_details = loc_value
                breakdown.append(f"Note: Using closest match '{city_name}' for '{place_name_input}'")
                break
    
    # If still not found, use a default area for QRD pricing
    if not location_details:
        msg = f"Details for '{place_name_input}{' - ' + suburb_name_input if suburb_name_input else ''}' not found in 2HomeAreas."
        breakdown.append(f"Note: {msg} Using QRD pricing.")
        # Return QRD pricing
        return jsonify({"message": "Quote Required (QRD)", "cost": "QRD", "breakdown": breakdown}), 200

    area_rating_str = location_details["area_rating"]
    rate_table_city_key_str = location_details["rate_table_city_key"]

    breakdown.append(f"Selected User Location: {place_name_input}{' (' + suburb_name_input + ')' if suburb_name_input else ''}")
    breakdown.append(f"Area Rating (from 2Home): {area_rating_str}")
    breakdown.append(f"Rate Table City Key (for Base Rate): {rate_table_city_key_str}")

    if area_rating_str.upper() == "QRD":
        breakdown.append("Result: Quote Required for this area.")
        return jsonify({"message": "Quote Required (QRD)", "cost": "QRD", "breakdown": breakdown}), 200
        
    # 3. Get components for the formula
    city_base_rate_per_m3 = shipping_data["city_base_rates"].get(rate_table_city_key_str)
    if city_base_rate_per_m3 is None:
         msg = f"Base rate per m³ for city '{rate_table_city_key_str}' not found in Rate Application data."
         breakdown.append(f"Error: {msg}")
         return jsonify({"error": msg, "cost": None, "breakdown": breakdown}), 500

    current_area_data = shipping_data["area_data"].get(area_rating_str)
    if not current_area_data:
        msg = f"Data for Area '{area_rating_str}' (delivery fee, extra per m³) not found."
        breakdown.append(f"Error: {msg}")
        return jsonify({"error": msg, "cost": None, "breakdown": breakdown}), 500

    area_delivery_fee = current_area_data["delivery_fee"]
    area_extra_per_m3 = current_area_data["extra_per_m3"]

    breakdown.append(f"City Base Rate per m³: ${city_base_rate_per_m3:.2f}")
    breakdown.append(f"Area Delivery Fee: ${area_delivery_fee:.2f}")
    breakdown.append(f"Area Extra $ per m³: ${area_extra_per_m3:.2f}")

    # 4. Calculate the final price using the formula
    # Total Rate = (City_Base_Rate_per_m3 + Area_Extra_$_per_m3) * Order_m3 + Area_Delivery_Fee
    final_price = (city_base_rate_per_m3 + area_extra_per_m3) * order_m3 + area_delivery_fee
    
    breakdown.append(f"Calculation: (${city_base_rate_per_m3:.2f} + ${area_extra_per_m3:.2f}) * {order_m3:.3f} m³ + ${area_delivery_fee:.2f}")
    breakdown.append(f"Total Shipping Cost: ${final_price:.2f}")
    return jsonify({"cost": f"{final_price:.2f}", "breakdown": breakdown}), 200


if __name__ == '__main__':
    # For production deployment, Railway will handle this
    # Load data once when the script starts
    if os.environ.get("WERKZEUG_RUN_MAIN") == "true" or not app.debug:
        load_shipping_data_on_startup()
    
    # Get port from environment variable (Railway will set this)
    port = int(os.environ.get("PORT", 5000))
    app.run(host="0.0.0.0", port=port, debug=False)  # debug=False for production
