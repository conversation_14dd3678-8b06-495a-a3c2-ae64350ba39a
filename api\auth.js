const express = require('express');
const { PrismaClient } = require('@prisma/client');
const crypto = require('crypto');
const axios = require('axios');

const router = express.Router();
const prisma = new PrismaClient();

// Helper function to verify Shopify webhook
function verifyShopifyWebhook(data, hmacHeader) {
  const calculated_hmac = crypto
    .createHmac('sha256', process.env.SHOPIFY_WEBHOOK_SECRET)
    .update(data, 'utf8')
    .digest('base64');
  
  return calculated_hmac === hmacHeader;
}

// OAuth - Step 1: Redirect to Shopify
router.get('/shopify', (req, res) => {
  const { shop } = req.query;
  
  if (!shop) {
    return res.status(400).json({ error: 'Shop parameter is required' });
  }
  
  const state = crypto.randomBytes(16).toString('hex');
  const redirectUri = `${process.env.HOST}/api/auth/shopify/callback`;
  const scopes = process.env.SCOPES;
  
  const authUrl = `https://${shop}.myshopify.com/admin/oauth/authorize?` +
    `client_id=${process.env.SHOPIFY_API_KEY}&` +
    `scope=${scopes}&` +
    `redirect_uri=${redirectUri}&` +
    `state=${state}`;
  
  // Store state in session or database for verification
  res.redirect(authUrl);
});

// OAuth - Step 2: Handle callback
router.get('/shopify/callback', async (req, res) => {
  const { code, hmac, shop, state, timestamp } = req.query;
  
  if (!code || !shop) {
    return res.status(400).json({ error: 'Missing required parameters' });
  }
  
  try {
    // Exchange code for access token
    const shopDomain = shop.replace('.myshopify.com', '') + '.myshopify.com';
    const tokenResponse = await axios.post(`https://${shopDomain}/admin/oauth/access_token`, {
      client_id: process.env.SHOPIFY_API_KEY,
      client_secret: process.env.SHOPIFY_API_SECRET,
      code
    });
    
    const { access_token } = tokenResponse.data;
    
    // Store shop and access token in database
    const shopRecord = await prisma.shop.upsert({
      where: { shopDomain: shopDomain },
      update: { 
        accessToken: access_token,
        updatedAt: new Date()
      },
      create: {
        shopDomain: shopDomain,
        accessToken: access_token
      }
    });
    
    // Redirect to app
    res.redirect(`https://${shop}.myshopify.com/admin/apps`);
  } catch (error) {
    console.error('OAuth callback error:', error);
    res.status(500).json({ error: 'Authentication failed' });
  }
});

// Verify installation
router.get('/verify', async (req, res) => {
  const { shop } = req.query;
  
  if (!shop) {
    return res.status(400).json({ error: 'Shop parameter is required' });
  }
  
  try {
    const shopRecord = await prisma.shop.findUnique({
      where: { shopDomain: shop }
    });
    
    if (!shopRecord || !shopRecord.accessToken) {
      return res.status(401).json({ error: 'Shop not authenticated' });
    }
    
    res.json({ authenticated: true, shop: shopRecord.shopDomain });
  } catch (error) {
    console.error('Verification error:', error);
    res.status(500).json({ error: 'Verification failed' });
  }
});

module.exports = router; 