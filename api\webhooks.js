const express = require('express');
const { PrismaClient } = require('@prisma/client');
const crypto = require('crypto');

const router = express.Router();
const prisma = new PrismaClient();

// Middleware to verify Shopify webhook
function verifyWebhook(req, res, next) {
  const hmac = req.get('X-Shopify-Hmac-Sha256');
  const body = JSON.stringify(req.body);
  const hash = crypto
    .createHmac('sha256', process.env.SHOPIFY_WEBHOOK_SECRET)
    .update(body, 'utf8')
    .digest('base64');

  if (hash !== hmac) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  next();
}

// App uninstall webhook
router.post('/app/uninstalled', verifyWebhook, async (req, res) => {
  try {
    const { domain } = req.body;
    
    // Mark shop as inactive when app is uninstalled
    await prisma.shop.update({
      where: { shopDomain: domain },
      data: { 
        isActive: false,
        accessToken: null,
        updatedAt: new Date()
      }
    });
    
    console.log(`App uninstalled for shop: ${domain}`);
    res.status(200).json({ success: true });
  } catch (error) {
    console.error('App uninstall webhook error:', error);
    res.status(500).json({ error: 'Webhook processing failed' });
  }
});

// Order created webhook
router.post('/orders/create', verifyWebhook, async (req, res) => {
  try {
    const order = req.body;
    const shopDomain = req.get('X-Shopify-Shop-Domain');
    
    // You can process the order here if needed
    // For example, create shipping labels, update inventory, etc.
    
    console.log(`Order created: ${order.id} for shop: ${shopDomain}`);
    res.status(200).json({ success: true });
  } catch (error) {
    console.error('Order create webhook error:', error);
    res.status(500).json({ error: 'Webhook processing failed' });
  }
});

// Order updated webhook
router.post('/orders/updated', verifyWebhook, async (req, res) => {
  try {
    const order = req.body;
    const shopDomain = req.get('X-Shopify-Shop-Domain');
    
    // Process order updates if needed
    
    console.log(`Order updated: ${order.id} for shop: ${shopDomain}`);
    res.status(200).json({ success: true });
  } catch (error) {
    console.error('Order update webhook error:', error);
    res.status(500).json({ error: 'Webhook processing failed' });
  }
});

// Order paid webhook
router.post('/orders/paid', verifyWebhook, async (req, res) => {
  try {
    const order = req.body;
    const shopDomain = req.get('X-Shopify-Shop-Domain');
    
    // Process paid orders if needed
    // This is where you might trigger shipping label creation
    
    console.log(`Order paid: ${order.id} for shop: ${shopDomain}`);
    res.status(200).json({ success: true });
  } catch (error) {
    console.error('Order paid webhook error:', error);
    res.status(500).json({ error: 'Webhook processing failed' });
  }
});

// Customer created webhook
router.post('/customers/create', verifyWebhook, async (req, res) => {
  try {
    const customer = req.body;
    const shopDomain = req.get('X-Shopify-Shop-Domain');
    
    // Process new customers if needed
    
    console.log(`Customer created: ${customer.id} for shop: ${shopDomain}`);
    res.status(200).json({ success: true });
  } catch (error) {
    console.error('Customer create webhook error:', error);
    res.status(500).json({ error: 'Webhook processing failed' });
  }
});

// Generic webhook handler for testing
router.post('/test', verifyWebhook, (req, res) => {
  console.log('Test webhook received:', req.body);
  res.status(200).json({ success: true, message: 'Test webhook received' });
});

module.exports = router; 