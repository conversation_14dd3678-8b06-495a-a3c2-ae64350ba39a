# Shopify App Configuration
SHOPIFY_API_KEY=your_shopify_api_key
SHOPIFY_API_SECRET=your_shopify_api_secret
SHOPIFY_WEBHOOK_SECRET=your_webhook_secret
SCOPES=read_orders,write_orders,read_customers,write_customers,write_shipping
HOST=https://your-app-url.com

# Database
DATABASE_URL="postgresql://username:password@localhost:5432/mainfreight_shopify"

# Mainfreight API Configuration
MAINFREIGHT_API_KEY=your_mainfreight_api_key
MAINFREIGHT_BASE_URL=https://api.mainfreight.com/transport/1.0/customer

# App Configuration
PORT=8080
NODE_ENV=development 