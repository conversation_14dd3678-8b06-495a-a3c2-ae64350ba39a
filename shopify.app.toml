# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

name = "mainfreight-shipping"
client_id = "your_client_id_here"
application_url = "https://mainfreight-shopify-production.up.railway.app"
embedded = true

[access_scopes]
# https://shopify.dev/docs/api/usage/access-scopes
scopes = "read_orders,write_orders,read_customers,write_customers,write_shipping"

[auth]
redirect_urls = [
  "https://mainfreight-shopify-production.up.railway.app/auth/callback",
  "https://mainfreight-shopify-production.up.railway.app/auth/shopify/callback",
  "https://mainfreight-shopify-production.up.railway.app/api/auth/callback"
]

[webhooks]
api_version = "2023-10"

[pos]
embedded = false

[build]
automatically_update_urls_on_dev = true
dev_store_url = "testformainfrieght.myshopify.com"
include_config_on_deploy = true

[web]
type = "backend"
commands.build = "npm run build"
commands.dev = "npm run dev"

[web.env]
PORT = "8080"

[[web.files]]
match = "api/**/*"
source = "api"

[[web.files]]
match = "web/**/*"
source = "web" 