const express = require('express');
const { PrismaClient } = require('@prisma/client');
const axios = require('axios');

const router = express.Router();
const prisma = new PrismaClient();

// Helper function to map Shopify address to Mainfreight format
function mapShopifyAddress(address, countryCode) {
  const mapped = {
    countryCode: countryCode || address.country_code || address.country
  };
  
  // Map based on country requirements
  if (countryCode === 'NZ') {
    mapped.suburb = address.address1 || address.city;
    mapped.city = address.city;
    mapped.postCode = address.zip || address.postal_code;
  } else if (countryCode === 'AU') {
    mapped.town = address.city;
    mapped.postCode = address.zip || address.postal_code;
    mapped.stateCode = address.province_code || address.state;
  } else {
    // Generic mapping for other countries
    mapped.city = address.city;
    mapped.postCode = address.zip || address.postal_code;
    mapped.stateCode = address.province_code || address.state;
  }
  
  return mapped;
}

// Helper function to calculate freight details from cart items
// Note: In this implementation, Shopify's weight field stores cubic meters (not actual weight)
function calculateFreightDetails(items) {
  let totalWeight = 0;
  let totalVolume = 0;
  let units = 0;
  
  items.forEach(item => {
    // Convert weight field (stored as grams) to cubic meters
    // If item.grams = 700, that represents 0.7 cubic meters
    const volumeM3 = item.grams ? item.grams / 1000 : 0.01; // Convert to m³
    const quantity = item.quantity || 1;
    
    totalVolume += volumeM3 * quantity;
    units += quantity;
    
    // Estimate weight from volume using average density
    // Assuming freight density of ~150 kg/m³ (adjust as needed)
    const estimatedWeight = volumeM3 * 150; // kg per m³
    totalWeight += estimatedWeight * quantity;
  });
  
  return [{
    units: Math.max(1, units),
    packTypeCode: "CTN", // Default to carton
    weight: Math.max(1, Math.round(totalWeight)),
    volume: Math.max(0.01, parseFloat(totalVolume.toFixed(3))),
    height: 1.0,
    length: 1.0,
    width: 1.0
  }];
}

// Shopify Carrier Service endpoint
router.post('/rates', async (req, res) => {
  try {
    const { rate } = req.body;
    
    if (!rate) {
      return res.status(400).json({ error: 'Invalid request format' });
    }
    
    const { origin, destination, items } = rate;
    
    const shopDomain = req.headers['x-shopify-shop-domain'] || req.query.shop;
    
    if (!shopDomain) {
      return res.json({ rates: [] });
    }
    
    const shop = await prisma.shop.findUnique({
      where: { shopDomain: shopDomain }
    });
    
    if (!shop || !shop.enableShipping) {
      return res.json({ rates: [] });
    }

    let shopifyRates = [];

    if (shop.activeShippingProvider === 'FURNITURE_WORLD' && shop.furnitureWorldApiUrl) {
      // Use Furniture World API
      const totalVolume = items.reduce((sum, item) => {
        // Using weight field as cubic meters (item.grams / 1000 = cubic meters)
        const itemVolume = item.grams ? item.grams / 1000 : 0.01; // Convert to m³
        return sum + (itemVolume * item.quantity);
      }, 0);
      // Estimate weight from volume using average freight density (~150 kg/m³)
      const totalWeightKg = totalVolume * 150;

      const furnitureWorldPayload = {
        place_name: destination.city, // Adjust as needed based on Furniture World API requirements
        suburb_name: destination.address1, // Or destination.address2, depending on what maps to suburb
        actual_m3: parseFloat(totalVolume.toFixed(3)) || 0.1, // Ensure non-zero volume
        weight_kg: parseFloat(totalWeightKg.toFixed(3)) || 0.1 // Ensure non-zero weight
      };

      try {
        const fwApiResponse = await axios.post(`${shop.furnitureWorldApiUrl.replace(/\/$/, '')}/calculate_shipping`, furnitureWorldPayload, {
          headers: { 'Content-Type': 'application/json' }
        });

        if (fwApiResponse.data && fwApiResponse.data.cost) {
          const cost = parseFloat(fwApiResponse.data.cost);
          if (!isNaN(cost)) {
            // Add 15% GST to the cost
            const costWithGST = cost * 1.15;
            // Apply minimum rate of $75
            const finalCost = Math.max(costWithGST, 75);
            shopifyRates.push({
              service_name: `Furniture World Shipping`,
              service_code: `furniture_world_shipping`,
              total_price: Math.round(finalCost * 100), // Convert to cents with GST
              currency: destination.currency || 'NZD',
              description: `Shipping (incl. 15% GST) • ${totalVolume.toFixed(2)}m³`,
            });
          } else if (fwApiResponse.data.cost === 'QRD') {
            // QRD tiered pricing based on cubic meters
            let qrdPrice = 0;
            let priceDescription = '';
            
            if (totalVolume <= 1.0) {
              qrdPrice = 300; // $300 for 0-1 cubic meter
              priceDescription = '0-1m³ rate';
            } else if (totalVolume <= 3.0) {
              qrdPrice = 500; // $500 for 1-3 cubic meter
              priceDescription = '1-3m³ rate';
            } else {
              // For shipments >3m³, use a higher rate or contact for quote
              qrdPrice = 750; // $750 for >3 cubic meter (you can adjust this)
              priceDescription = 'Large shipment rate';
            }
            
            // Add 15% GST to the QRD price
            const qrdPriceWithGST = qrdPrice * 1.15;
            // Apply minimum rate of $75
            const finalQrdPrice = Math.max(qrdPriceWithGST, 75);
            
            shopifyRates.push({
              service_name: `Furniture World - ${priceDescription}`,
              service_code: `furniture_world_qrd`,
              total_price: Math.round(finalQrdPrice * 100), // Convert to cents with GST
              currency: destination.currency || 'NZD',
              description: `QRD Shipping (incl. 15% GST) • ${totalVolume.toFixed(2)}m³`,
            });
          }
        }
      } catch (fwError) {
        console.error('Furniture World API error:', fwError.response ? fwError.response.data : fwError.message);
        // Optionally, add a generic error rate or message to Shopify
      }

    } else if (shop.activeShippingProvider === 'MAIN Словарь' && shop.mainfreightApiKey) {
      // Use Mainfreight API (existing logic)
      const originAddress = mapShopifyAddress(origin, shop.mainfreightRegion);
      const destinationAddress = mapShopifyAddress(destination, shop.mainfreightRegion);
      const freightDetails = calculateFreightDetails(items);
      
      const mainfreightPayload = {
        account: { code: shop.mainfreightAccountCode },
        serviceLevel: { code: shop.defaultServiceLevel || "LCL" },
        origin: {
          freightRequiredDateTime: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
          freightRequiredDateTimeZone: "UTC",
          address: originAddress
        },
        destination: { address: destinationAddress },
        freightDetails: freightDetails
      };
      
      try {
        const response = await axios.post(
          `https://api.mainfreight.com/transport/1.0/customer/rate?region=${shop.mainfreightRegion}`,
          mainfreightPayload,
          {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Secret ${shop.mainfreightApiKey}`,
              'Accept': 'application/json'
            }
          }
        );
        
        const { charges } = response.data;
        const totalAmount = charges.find(charge => charge.name === 'TotalExcludingGSTAmount')?.value || 0;
        
        // Add 15% GST to the total amount
        const totalWithGST = totalAmount * 1.15;
        // Apply minimum rate of $75
        const finalMainfreightPrice = Math.max(totalWithGST, 75);
        
        // Get weight and volume for description
        const totalWeight = freightDetails.reduce((sum, detail) => sum + detail.weight, 0);
        const totalVolume = freightDetails.reduce((sum, detail) => sum + (detail.volume || 0), 0);
        
        await prisma.shippingRate.create({
          data: {
            shopId: shop.id,
            origin: originAddress,
            destination: destinationAddress,
            freightDetails: freightDetails,
            charges: charges,
            totalAmount: finalMainfreightPrice // Store final amount with minimum
          }
        });
        
        shopifyRates.push({
          service_name: `Mainfreight ${shop.defaultServiceLevel}`,
          service_code: `mainfreight_${shop.defaultServiceLevel.toLowerCase()}`,
          total_price: Math.round(finalMainfreightPrice * 100), // Convert to cents with minimum applied
          currency: destination.currency || 'NZD',
          description: `Shipping (incl. 15% GST) • ${totalVolume.toFixed(2)}m³ • 3-7 business days`,
          min_delivery_date: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
          max_delivery_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
        });
      } catch (mfError) {
        console.error('Mainfreight API error:', mfError.response ? mfError.response.data : mfError.message);
      }
    }
    
    res.json({ rates: shopifyRates });
    
  } catch (error) {
    console.error('Shipping rates error:', error);
    res.json({ rates: [] });
  }
});

// Manual rate calculation endpoint for testing
router.post('/calculate', async (req, res) => {
  const { shop: shopDomain } = req.query;
  const { origin, destination, freightDetails } = req.body;
  
  if (!shopDomain) {
    return res.status(400).json({ error: 'Shop parameter is required' });
  }
  
  try {
    const shop = await prisma.shop.findUnique({
      where: { shopDomain: shopDomain }
    });
    
    if (!shop || !shop.mainfreightApiKey) {
      return res.status(400).json({ error: 'Shop not configured for shipping' });
    }
    
    const mainfreightPayload = {
      account: {
        code: shop.mainfreightAccountCode
      },
      serviceLevel: {
        code: shop.defaultServiceLevel || "LCL"
      },
      origin: {
        freightRequiredDateTime: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        freightRequiredDateTimeZone: "UTC",
        address: origin
      },
      destination: {
        address: destination
      },
      freightDetails: freightDetails
    };
    
    const response = await axios.post(
      `https://api.mainfreight.com/transport/1.0/customer/rate?region=${shop.mainfreightRegion}`,
      mainfreightPayload,
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Secret ${shop.mainfreightApiKey}`,
          'Accept': 'application/json'
        }
      }
    );
    
    // Calculate total with GST
    const baseAmount = response.data.charges.find(c => c.name === 'TotalExcludingGSTAmount')?.value || 0;
    const totalWithGST = baseAmount * 1.15;
    // Apply minimum rate of $75
    const finalAmount = Math.max(totalWithGST, 75);
    
    // Store the calculation
    await prisma.shippingRate.create({
      data: {
        shopId: shop.id,
        origin: origin,
        destination: destination,
        freightDetails: freightDetails,
        charges: response.data.charges,
        totalAmount: finalAmount
      }
    });
    
    res.json({
      success: true,
      data: {
        ...response.data,
        totalWithGST: totalWithGST,
        finalAmount: finalAmount,
        minimumApplied: finalAmount > totalWithGST,
        baseAmount: baseAmount,
        gstAmount: totalWithGST - baseAmount
      },
      calculation: mainfreightPayload
    });
    
  } catch (error) {
    console.error('Manual calculation error:', error);
    
    if (error.response) {
      res.status(400).json({
        success: false,
        error: error.response.data || error.message
      });
    } else {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }
});

// Get shipping history
router.get('/history', async (req, res) => {
  const { shop: shopDomain } = req.query;
  const { page = 1, limit = 20 } = req.query;
  
  if (!shopDomain) {
    return res.status(400).json({ error: 'Shop parameter is required' });
  }
  
  try {
    const shop = await prisma.shop.findUnique({
      where: { shopDomain: shopDomain }
    });
    
    if (!shop) {
      return res.status(404).json({ error: 'Shop not found' });
    }
    
    const skip = (parseInt(page) - 1) * parseInt(limit);
    
    const [rates, total] = await Promise.all([
      prisma.shippingRate.findMany({
        where: { shopId: shop.id },
        orderBy: { createdAt: 'desc' },
        skip: skip,
        take: parseInt(limit)
      }),
      prisma.shippingRate.count({
        where: { shopId: shop.id }
      })
    ]);
    
    res.json({
      rates,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    });
    
  } catch (error) {
    console.error('Get history error:', error);
    res.status(500).json({ error: 'Failed to fetch shipping history' });
  }
});

module.exports = router; 