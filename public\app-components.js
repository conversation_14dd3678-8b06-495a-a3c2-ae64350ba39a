// Shipping History Component
function ShippingHistory() {
  const [rates, setRates] = useState([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [pagination, setPagination] = useState(null);

  const loadHistory = useCallback(async (pageNum = 1) => {
    setLoading(true);
    try {
      const response = await api.get(`/shipping/history&page=${pageNum}&limit=10`);
      setRates(response.data.rates);
      setPagination(response.data.pagination);
    } catch (error) {
      console.error('Failed to load shipping history:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadHistory(page);
  }, [page, loadHistory]);

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString();
  };

  const formatCurrency = (amount) => {
    return `$${parseFloat(amount).toFixed(2)}`;
  };

  if (loading) {
    return Loading();
  }

  return React.createElement('div', {
    style: { maxWidth: '800px', margin: '0 auto', padding: '20px' }
  }, [
    React.createElement('h2', {
      key: 'title',
      style: { marginBottom: '24px', color: '#212b36' }
    }, 'Shipping History'),
    
    React.createElement('div', {
      key: 'content',
      style: { backgroundColor: 'white', borderRadius: '8px', boxShadow: '0 1px 3px rgba(0,0,0,0.1)' }
    }, [
      rates.length === 0 ? React.createElement('div', {
        key: 'empty',
        style: { padding: '40px', textAlign: 'center', color: '#637381' }
      }, 'No shipping calculations yet') : React.createElement('div', {
        key: 'table',
        style: { overflowX: 'auto' }
      }, [
        React.createElement('table', {
          key: 'table',
          style: { width: '100%', borderCollapse: 'collapse' }
        }, [
          React.createElement('thead', { key: 'head' }, [
            React.createElement('tr', { key: 'row' }, [
              React.createElement('th', { key: 'date', style: { padding: '16px', textAlign: 'left', borderBottom: '1px solid #e1e3e5' } }, 'Date'),
              React.createElement('th', { key: 'origin', style: { padding: '16px', textAlign: 'left', borderBottom: '1px solid #e1e3e5' } }, 'Origin'),
              React.createElement('th', { key: 'dest', style: { padding: '16px', textAlign: 'left', borderBottom: '1px solid #e1e3e5' } }, 'Destination'),
              React.createElement('th', { key: 'amount', style: { padding: '16px', textAlign: 'right', borderBottom: '1px solid #e1e3e5' } }, 'Amount')
            ])
          ]),
          React.createElement('tbody', { key: 'body' }, rates.map((rate, index) => 
            React.createElement('tr', { key: rate.id }, [
              React.createElement('td', { key: 'date', style: { padding: '16px', borderBottom: '1px solid #e1e3e5' } }, formatDate(rate.createdAt)),
              React.createElement('td', { key: 'origin', style: { padding: '16px', borderBottom: '1px solid #e1e3e5' } }, `${rate.origin.city || 'N/A'}, ${rate.origin.countryCode}`),
              React.createElement('td', { key: 'dest', style: { padding: '16px', borderBottom: '1px solid #e1e3e5' } }, `${rate.destination.city || 'N/A'}, ${rate.destination.countryCode}`),
              React.createElement('td', { key: 'amount', style: { padding: '16px', borderBottom: '1px solid #e1e3e5', textAlign: 'right' } }, formatCurrency(rate.totalAmount))
            ])
          ))
        ])
      ]),
      
      // Pagination
      pagination && pagination.pages > 1 && React.createElement('div', {
        key: 'pagination',
        style: { padding: '16px', borderTop: '1px solid #e1e3e5', display: 'flex', justifyContent: 'center', gap: '8px' }
      }, [
        React.createElement('button', {
          key: 'prev',
          onClick: () => setPage(Math.max(1, page - 1)),
          disabled: page === 1,
          style: {
            padding: '8px 16px',
            backgroundColor: page === 1 ? '#f6f6f7' : '#008060',
            color: page === 1 ? '#637381' : 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: page === 1 ? 'not-allowed' : 'pointer'
          }
        }, 'Previous'),
        
        React.createElement('span', {
          key: 'info',
          style: { padding: '8px 16px', color: '#637381' }
        }, `Page ${page} of ${pagination.pages}`),
        
        React.createElement('button', {
          key: 'next',
          onClick: () => setPage(Math.min(pagination.pages, page + 1)),
          disabled: page === pagination.pages,
          style: {
            padding: '8px 16px',
            backgroundColor: page === pagination.pages ? '#f6f6f7' : '#008060',
            color: page === pagination.pages ? '#637381' : 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: page === pagination.pages ? 'not-allowed' : 'pointer'
          }
        }, 'Next')
      ])
    ])
  ]);
}

// Navigation Tabs Component
function NavigationTabs({ activeTab, onTabChange }) {
  const tabs = [
    { id: 'settings', label: 'Settings' },
    { id: 'history', label: 'Shipping History' }
  ];

  return React.createElement('div', {
    style: { 
      backgroundColor: 'white', 
      borderBottom: '1px solid #e1e3e5', 
      marginBottom: '24px' 
    }
  }, React.createElement('div', {
    style: { 
      maxWidth: '800px', 
      margin: '0 auto', 
      display: 'flex' 
    }
  }, tabs.map(tab => 
    React.createElement('button', {
      key: tab.id,
      onClick: () => onTabChange(tab.id),
      style: {
        padding: '16px 24px',
        backgroundColor: 'transparent',
        border: 'none',
        borderBottom: activeTab === tab.id ? '2px solid #008060' : '2px solid transparent',
        cursor: 'pointer',
        fontSize: '14px',
        fontWeight: activeTab === tab.id ? '600' : '400',
        color: activeTab === tab.id ? '#008060' : '#637381'
      }
    }, tab.label)
  )));
}

// Main App Component
function App() {
  const [settings, setSettings] = useState(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('settings');
  const [toast, setToast] = useState(null);

  const showToast = (message, type = 'info') => {
    setToast({ message, type });
  };

  const hideToast = () => {
    setToast(null);
  };

  const loadSettings = useCallback(async () => {
    setLoading(true);
    try {
      const response = await api.get('/settings');
      setSettings(response.data);
    } catch (error) {
      console.error('Failed to load settings:', error);
      showToast('Failed to load settings', 'error');
    } finally {
      setLoading(false);
    }
  }, []);

  const saveSettings = async (newSettings) => {    try {      const response = await api.put('/settings', newSettings);      setSettings(response.data.settings);            let message = 'Settings saved successfully';      if (response.data.carrierServiceActive) {        message += ' and carrier service registered with Shopify';      } else if (response.data.carrierServiceError) {        message += `, but carrier service registration failed: ${response.data.carrierServiceError}`;      }            showToast(message, response.data.carrierServiceError ? 'error' : 'success');    } catch (error) {      console.error('Failed to save settings:', error);      showToast('Failed to save settings', 'error');    }  };

  useEffect(() => {
    const shopDomain = getShopDomain();
    if (!shopDomain) {
      showToast('Shop domain not found in URL', 'error');
      return;
    }
    loadSettings();
  }, [loadSettings]);

  if (loading) {
    return Loading();
  }

  if (!settings) {
    return React.createElement('div', {
      className: 'error'
    }, [
      React.createElement('h1', { key: 'title' }, 'Error'),
      React.createElement('p', { key: 'message' }, 'Failed to load app settings. Please try again.'),
      React.createElement('button', {
        key: 'retry',
        className: 'retry-btn',
        onClick: loadSettings
      }, 'Retry')
    ]);
  }

  return React.createElement('div', null, [
    // Toast notification
    toast && React.createElement(Toast, {
      key: 'toast',
      message: toast.message,
      type: toast.type,
      onDismiss: hideToast
    }),

    // Navigation
    React.createElement(NavigationTabs, {
      key: 'nav',
      activeTab: activeTab,
      onTabChange: setActiveTab
    }),

    // Content
    React.createElement('div', {
      key: 'content',
      style: { minHeight: 'calc(100vh - 120px)' }
    }, activeTab === 'settings' 
      ? React.createElement(SettingsForm, {
          settings: settings,
          onSave: saveSettings
        })
      : React.createElement(ShippingHistory)
    )
  ]);
}

// Initialize the app
document.addEventListener('DOMContentLoaded', () => {
  const root = ReactDOM.createRoot(document.getElementById('root'));
  root.render(React.createElement(App));
}); 